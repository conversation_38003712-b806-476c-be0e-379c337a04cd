<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FeexVeb Test</title>
  <script type="importmap">
    {
      "imports": {
        "@maverick-js/signals": "https://esm.sh/@maverick-js/signals@5.11.5"
      }
    }
    </script>
</head>

<body>
  <h1>FeexVeb Component Test</h1>
  <test-component></test-component>

  <script>
    console.log('Basic script loaded');
    document.addEventListener('DOMContentLoaded', () => {
      console.log('DOM loaded');
      document.body.innerHTML += '<p style="color: red;">JavaScript is working!</p>';
    });
  </script>
  <script type="module">
    console.log('Module script starting');
    try {
      console.log('About to import test component');
      import('./test-component.js').then(() => {
        console.log('Test component imported successfully');
      }).catch(error => {
        console.error('Failed to import test component:', error);
      });
    } catch (error) {
      console.error('Error in module script:', error);
    }
  </script>
</body>

</html>