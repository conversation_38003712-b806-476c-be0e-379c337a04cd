/**
 * @file FeexVeb Public API
 * @description Main entry point for the FeexVeb library with organized exports
 */

// Re-export the main FeexVeb namespace as default
export { default } from './feexveb.js';

// Core functionality exports
export {
  // WebJSX functions
  createElement,
  Fragment,
  createDomNode,
  applyDiff,
  renderToString
} from './src/webjsx.js';

export {
  // State management
  useState,
  useComputed,
  useEffect
} from './src/state.js';

export {
  // Component definition
  defineComponent,
  component
} from './src/component.js';

export {
  // Event system
  createEventBus
} from './src/eventbus.js';

// Utility exports
export {
  // Attribute utilities
  attr,
  numAttr,
  dashToCamelCase,
  camelToDashCase,
  attributeHelpers,
  mountHelpers,
  testHelpers
} from './src/utils.js';

// HTMX integration
export {
  initHtmx,
  configureHtmx,
  processHtmx,
  htmxAttrs,
  triggerHtmx
} from './src/htmx_integration.js';

// Styling utilities
export {
  monospaceCss,
  monospaceCssForHtml,
  createMonospaceStyleElement,
  injectMonospaceStyles
} from './src/monospace-styles.js';

// Rendering utilities
export {
  clientRenderer,
  attributeHelpers as renderAttributeHelpers,
  performanceHelpers,
  errorHelpers,
  devHelpers
} from './src/renderer.js';

// Server-side rendering (optional)
export {
  ServerFeexVeb,
  registerServerComponent,
  createSSRContext,
  createHydrationMarker,
  renderPage
} from './src/server-renderer.js';

/**
 * Convenience shortcuts for common operations
 */
export const shortcuts = {
  /**
   * Quick component creation with minimal boilerplate
   * @param {string} tag - Component tag name
   * @param {Function} render - Render function
   * @param {Object} options - Additional options
   */
  quickComponent(tag, render, options = {}) {
    const { default: FeexVeb } = await import('./feexveb.js');
    return FeexVeb.component({
      tag,
      render,
      ...options
    });
  },

  /**
   * Create a simple stateful component
   * @param {string} tag - Component tag name
   * @param {Object} initialState - Initial state object
   * @param {Function} render - Render function receiving state
   */
  statefulComponent(tag, initialState, render) {
    const { default: FeexVeb } = await import('./feexveb.js');
    return FeexVeb.component({
      tag,
      state: initialState,
      render
    });
  },

  /**
   * Mount a component to a specific element
   * @param {string} tag - Component tag name
   * @param {HTMLElement} target - Target element
   * @param {Object} props - Component properties
   */
  async mount(tag, target, props = {}) {
    const element = document.createElement(tag);
    Object.entries(props).forEach(([key, value]) => {
      element.setAttribute(key, String(value));
    });
    target.appendChild(element);
    await customElements.whenDefined(tag);
    return element;
  }
};

/**
 * Version information
 */
export const version = '1.0.0';

/**
 * Feature flags for development/debugging
 */
export const features = {
  SSR_ENABLED: typeof globalThis !== 'undefined' && globalThis.Deno !== undefined,
  HTMX_AVAILABLE: typeof globalThis !== 'undefined' && globalThis.htmx !== undefined,
  DEV_MODE: typeof globalThis !== 'undefined' && globalThis.location?.hostname === 'localhost'
};