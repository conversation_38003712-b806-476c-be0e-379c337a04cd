/**
 * @file JSX Transpiler for FeexVeb
 * @description Converts JSX syntax to FeexVeb.createElement calls for browser compatibility
 */

/**
 * Simple JSX transpiler that converts JSX syntax to createElement calls
 * @param {string} jsxCode - JavaScript code containing JSX
 * @returns {string} - JavaScript code with JSX converted to createElement calls
 */
export function transpileJSX(jsxCode) {
  // Handle JSX elements with a more robust regex approach
  let result = jsxCode;

  // First, handle self-closing tags like <div />
  result = result.replace(
    /<(\w+)([^>]*?)\/>/g,
    (match, tagName, attributes) => {
      const props = parseAttributes(attributes);
      return `FeexVeb.createElement('${tagName}'${props ? `, ${props}` : ', null'})`;
    }
  );

  // Handle opening and closing tags
  // This is a simplified approach - for production, you'd want a proper AST parser
  result = transpileJSXElements(result);

  return result;
}

/**
 * Parse JSX attributes into a props object string
 * @param {string} attributeString - Raw attribute string from JSX
 * @returns {string} - JavaScript object string for props
 */
function parseAttributes(attributeString) {
  if (!attributeString || !attributeString.trim()) {
    return null;
  }

  const props = [];

  // Handle class attribute (convert to className for React compatibility, but keep as 'class' for FeexVeb)
  const classMatch = attributeString.match(/class=["']([^"']+)["']/);
  if (classMatch) {
    props.push(`class: '${classMatch[1]}'`);
  }

  // Handle dynamic class attribute
  const dynamicClassMatch = attributeString.match(/class=\{([^}]+)\}/);
  if (dynamicClassMatch) {
    props.push(`class: ${dynamicClassMatch[1]}`);
  }

  // Handle onclick and other event handlers
  const onclickMatch = attributeString.match(/onclick=\{([^}]+)\}/);
  if (onclickMatch) {
    props.push(`onclick: ${onclickMatch[1]}`);
  }

  // Handle other common attributes
  const idMatch = attributeString.match(/id=["']([^"']+)["']/);
  if (idMatch) {
    props.push(`id: '${idMatch[1]}'`);
  }

  // Handle dynamic attributes
  const dynamicAttrMatches = attributeString.matchAll(/(\w+)=\{([^}]+)\}/g);
  for (const match of dynamicAttrMatches) {
    const [, attrName, attrValue] = match;
    if (attrName !== 'class' && attrName !== 'onclick') {
      props.push(`${attrName}: ${attrValue}`);
    }
  }

  // Handle string attributes
  const stringAttrMatches = attributeString.matchAll(/(\w+)=["']([^"']+)["']/g);
  for (const match of stringAttrMatches) {
    const [, attrName, attrValue] = match;
    if (attrName !== 'class' && attrName !== 'id') {
      props.push(`${attrName}: '${attrValue}'`);
    }
  }

  return props.length > 0 ? `{ ${props.join(', ')} }` : null;
}

/**
 * Transpile JSX elements with opening and closing tags
 * @param {string} code - Code containing JSX elements
 * @returns {string} - Code with JSX converted to createElement calls
 */
function transpileJSXElements(code) {
  // This is a simplified implementation
  // For production, you'd want to use a proper JSX parser like Babel

  let result = code;

  // Handle simple cases first - elements with text content
  result = result.replace(
    /<(\w+)([^>]*?)>([^<]+)<\/\1>/g,
    (match, tagName, attributes, textContent) => {
      const props = parseAttributes(attributes);
      const content = textContent.trim();

      // Check if content contains variables (wrapped in {})
      if (content.includes('{') && content.includes('}')) {
        const processedContent = content.replace(/\{([^}]+)\}/g, '${$1}');
        return `FeexVeb.createElement('${tagName}'${props ? `, ${props}` : ', null'}, \`${processedContent}\`)`;
      } else {
        return `FeexVeb.createElement('${tagName}'${props ? `, ${props}` : ', null'}, '${content}')`;
      }
    }
  );

  return result;
}

/**
 * More sophisticated JSX transpiler using a stack-based approach
 * @param {string} jsxCode - JavaScript code containing JSX
 * @returns {string} - JavaScript code with JSX converted to createElement calls
 */
export function transpileJSXAdvanced(jsxCode) {
  // For now, use the simple transpiler
  // In a production system, you'd integrate with Babel or write a proper parser
  return transpileJSX(jsxCode);
}

/**
 * Check if code contains JSX syntax
 * @param {string} code - JavaScript code to check
 * @returns {boolean} - True if code contains JSX
 */
export function containsJSX(code) {
  // Simple heuristic to detect JSX
  return /<\w+[^>]*>/.test(code) || /<\/\w+>/.test(code) || /<\w+[^>]*\/>/.test(code);
}

/**
 * Transform JSX return statements to use createElement
 * @param {string} code - JavaScript code
 * @returns {string} - Transformed code
 */
export function transformJSXReturns(code) {
  // Handle return statements with JSX
  return code.replace(
    /return\s*\(\s*(<[\s\S]*?>[\s\S]*?<\/\w+>|<\w+[^>]*\/>)\s*\)/g,
    (match, jsxContent) => {
      const transpiled = transpileJSX(jsxContent);
      return `return ${transpiled}`;
    }
  );
}

/**
 * Process a complete component file with JSX
 * @param {string} componentCode - Complete component file content
 * @returns {string} - Processed component code without JSX
 */
export function processComponentFile(componentCode) {
  if (!containsJSX(componentCode)) {
    return componentCode;
  }

  let result = componentCode;

  // More comprehensive JSX transformation
  result = transformComplexJSX(result);

  return result;
}

/**
 * Transform complex JSX structures including nested elements
 * @param {string} code - JavaScript code with JSX
 * @returns {string} - Transformed code
 */
function transformComplexJSX(code) {
  let result = code;

  // Handle JSX in render functions and arrow functions
  result = result.replace(
    /(render:\s*\([^)]*\)\s*=>\s*)\(?\s*(<[\s\S]*?>[\s\S]*?)\s*\)?(\s*[,}])/g,
    (match, prefix, jsxContent, suffix) => {
      const transpiled = transformJSXBlock(jsxContent.trim());
      return `${prefix}${transpiled}${suffix}`;
    }
  );

  // Handle arrow function returns
  result = result.replace(
    /=>\s*\(\s*(<[\s\S]*?>[\s\S]*?)\s*\)/g,
    (match, jsxContent) => {
      const transpiled = transformJSXBlock(jsxContent.trim());
      return `=> ${transpiled}`;
    }
  );

  return result;
}

/**
 * Transform a block of JSX into createElement calls
 * @param {string} jsxBlock - Block of JSX code
 * @returns {string} - createElement calls
 */
function transformJSXBlock(jsxBlock) {
  // Handle simple self-closing tags
  let result = jsxBlock.replace(
    /<(\w+)([^>]*?)\/>/g,
    (match, tagName, attrs) => {
      const props = parseJSXAttributes(attrs);
      return `FeexVeb.createElement('${tagName}'${props})`;
    }
  );

  // Handle elements with content
  result = transformNestedJSX(result);

  return result;
}

/**
 * Parse JSX attributes more robustly
 * @param {string} attrString - Attribute string
 * @returns {string} - Props object string
 */
function parseJSXAttributes(attrString) {
  if (!attrString || !attrString.trim()) {
    return ', null';
  }

  const props = [];
  const trimmed = attrString.trim();

  // Parse different types of attributes
  const patterns = [
    // String attributes: class="value"
    { regex: /(\w+)=["']([^"']+)["']/g, handler: (name, value) => `${name}: '${value}'` },
    // Expression attributes: onclick={handler}
    { regex: /(\w+)=\{([^}]+)\}/g, handler: (name, value) => `${name}: ${value}` },
    // Boolean attributes: disabled
    { regex: /\b(\w+)(?=\s|$)/g, handler: (name) => `${name}: true` }
  ];

  for (const pattern of patterns) {
    const matches = [...trimmed.matchAll(pattern.regex)];
    for (const match of matches) {
      if (pattern.handler.length === 2) {
        props.push(pattern.handler(match[1], match[2]));
      } else {
        props.push(pattern.handler(match[1]));
      }
    }
  }

  return props.length > 0 ? `, { ${props.join(', ')} }` : ', null';
}

/**
 * Transform nested JSX elements
 * @param {string} jsx - JSX string
 * @returns {string} - createElement calls
 */
function transformNestedJSX(jsx) {
  // This is a simplified approach for basic JSX transformation
  // For production, you'd want a proper AST parser

  // Handle simple text content elements
  jsx = jsx.replace(
    /<(\w+)([^>]*?)>([^<{]+)<\/\1>/g,
    (match, tagName, attrs, content) => {
      const props = parseJSXAttributes(attrs);
      return `FeexVeb.createElement('${tagName}'${props}, '${content.trim()}')`;
    }
  );

  // Handle elements with expression content
  jsx = jsx.replace(
    /<(\w+)([^>]*?)>\{([^}]+)\}<\/\1>/g,
    (match, tagName, attrs, expression) => {
      const props = parseJSXAttributes(attrs);
      return `FeexVeb.createElement('${tagName}'${props}, ${expression})`;
    }
  );

  return jsx;
}
