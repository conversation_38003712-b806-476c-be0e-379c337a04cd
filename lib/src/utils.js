/**
 * @module utils
 * @description This module offers miscellaneous utility functions for working with HTML attributes,
 * string transformations, attribute schemas, and component mounting.
 */

/**
 * Retrieves the value of an attribute from an HTML element.
 * If the attribute does not exist, it returns a specified default value.
 *
 * @param {HTMLElement} element - The HTML element from which to get the attribute.
 * @param {string} name - The name of the attribute to retrieve.
 * @param {*} [defaultValue=''] - The value to return if the attribute is not found on the element.
 * @returns {string} The value of the attribute, or the `defaultValue` if the attribute is not set.
 *                   Note: All attribute values are returned as strings.
 */
export const attr = (element, name, defaultValue = '') => {
  const value = element.getAttribute(name);
  return value !== null ? value : defaultValue;
};

/**
 * Retrieves the numeric value of an attribute from an HTML element.
 * It attempts to convert the attribute's string value to a number.
 * If the attribute does not exist or its value cannot be converted to a number,
 * it returns a specified default numeric value.
 *
 * @param {HTMLElement} element - The HTML element from which to get the numeric attribute.
 * @param {string} name - The name of the attribute to retrieve.
 * @param {number} [defaultValue=0] - The numeric value to return if the attribute is not found
 *                                    or if its value is not a valid number.
 * @returns {number} The numeric value of the attribute, or the `defaultValue`.
 * @deprecated Use attributeHelpers.getTypedAttribute instead
 */
export const numAttr = (element, name, defaultValue = 0) => {
  const value = element.getAttribute(name);
  if (value === null || value.trim() === '') {
    return defaultValue;
  }
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
};

/**
 * Converts a dash-cased string to camelCase.
 * e.g., 'initial-count' -> 'initialCount'
 * @param {string} str The dash-cased string.
 * @returns {string} The camelCased string.
 */
export const dashToCamelCase = (str) => {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
};

/**
 * Converts a camelCase string to dash-case.
 * e.g., 'initialCount' -> 'initial-count'
 * @param {string} str The camelCase string.
 * @returns {string} The dash-cased string.
 */
export const camelToDashCase = (str) => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase();
};

/**
 * Attribute schema helpers for component attribute processing
 */
export const attributeHelpers = {
  /**
   * Processes an attribute schema configuration into a normalized format
   * @param {Object} attrsConfig - Raw attribute configuration
   * @returns {Object} Normalized attribute schema
   */
  normalizeSchema(attrsConfig) {
    const attributesSchema = {};
    Object.entries(attrsConfig).forEach(([attrName, config]) => {
      if (typeof config === 'string') {
        // Type specified: { 'initial-count': 'number' }
        attributesSchema[attrName] = config;
      } else if (typeof config === 'object' && config.type) {
        // Object with type: { 'initial-count': { type: 'number', default: 0 } }
        attributesSchema[attrName] = config.type;
      } else {
        // Infer type from default value
        const type = typeof config;
        attributesSchema[attrName] = type === 'object' ? 'string' : type;
      }
    });
    return attributesSchema;
  },

  /**
   * Coerces an attribute value to the specified type
   * @param {string|null} value - The attribute value
   * @param {string} type - The target type ('string', 'number', 'boolean')
   * @returns {*} The coerced value
   */
  coerceValue(value, type) {
    switch (type) {
      case 'string':
        return String(value);
      case 'number':
        return Number(value);
      case 'boolean':
        return value !== null && value !== 'false';
      default:
        return value;
    }
  },

  /**
   * Gets an attribute value with type coercion and default handling
   * @param {HTMLElement} element - The element to get the attribute from
   * @param {string} attrName - The attribute name
   * @param {string} type - The expected type
   * @param {*} defaultValue - The default value if attribute is missing
   * @returns {*} The typed attribute value
   */
  getTypedAttribute(element, attrName, type, defaultValue) {
    if (element.hasAttribute(attrName)) {
      const value = element.getAttribute(attrName);
      return this.coerceValue(value, type);
    }
    return defaultValue;
  }
};

/**
 * Component mounting utilities
 */
export const mountHelpers = {
  /**
   * Replaces component placeholders with actual custom elements
   * @param {Document|Element} root - The root element to search for placeholders
   */
  replaceComponentPlaceholders(root = document) {
    const placeholders = root.querySelectorAll('.component-placeholder');

    placeholders.forEach(placeholder => {
      const componentTag = placeholder.dataset.component;

      // Check if the custom element is defined
      if (customElements.get(componentTag)) {
        // Create the actual component element
        const componentElement = document.createElement(componentTag);

        // Copy all data attributes as regular attributes
        Object.keys(placeholder.dataset).forEach(key => {
          if (key !== 'component') {
            const attrName = key.replace(/([A-Z])/g, '-$1').toLowerCase();
            componentElement.setAttribute(attrName, placeholder.dataset[key]);
          }
        });

        // Replace the placeholder with the actual component
        placeholder.parentNode.replaceChild(componentElement, placeholder);
      }
    });
  },

  /**
   * Sets up automatic component placeholder replacement
   * @param {number[]} delays - Array of delays in milliseconds for retry attempts
   */
  setupAutoMount(delays = [0, 100, 500, 1000]) {
    delays.forEach(delay => {
      setTimeout(() => this.replaceComponentPlaceholders(), delay);
    });

    // Listen for custom element definitions and replace placeholders as they become available
    const observer = new MutationObserver(() => {
      this.replaceComponentPlaceholders();
    });

    // Start observing when components are added to the DOM
    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }
};

/**
 * Test helpers for component testing
 */
export const testHelpers = {
  /**
   * Creates a test fixture with a custom element
   * @param {string} tagName - The custom element tag name
   * @param {Object} attributes - Attributes to set on the element
   * @returns {HTMLElement} The created test element
   */
  createTestElement(tagName, attributes = {}) {
    const element = document.createElement(tagName);
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, String(value));
    });
    document.body.appendChild(element);
    return element;
  },

  /**
   * Cleans up test elements from the DOM
   * @param {HTMLElement|string} elementOrSelector - Element or selector to remove
   */
  cleanupTestElement(elementOrSelector) {
    const element = typeof elementOrSelector === 'string'
      ? document.querySelector(elementOrSelector)
      : elementOrSelector;
    if (element && element.parentNode) {
      element.parentNode.removeChild(element);
    }
  },

  /**
   * Waits for a custom element to be defined and connected
   * @param {string} tagName - The custom element tag name
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise<void>}
   */
  async waitForElement(tagName, timeout = 5000) {
    await customElements.whenDefined(tagName);
    
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Element ${tagName} did not connect within ${timeout}ms`));
      }, timeout);

      const checkElement = () => {
        const element = document.querySelector(tagName);
        if (element && element.isConnected) {
          clearTimeout(timeoutId);
          resolve();
        } else {
          setTimeout(checkElement, 10);
        }
      };
      checkElement();
    });
  }
};
