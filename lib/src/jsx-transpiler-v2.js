/**
 * @file Advanced JSX Transpiler for FeexVeb
 * @description Robust JSX to createElement transpiler with proper parsing
 */

/**
 * Main function to process component files with JSX
 * @param {string} code - JavaScript code containing JSX
 * @returns {string} - Transpiled JavaScript code
 */
export function processComponentFile(code) {
  if (!containsJSX(code)) {
    return code;
  }
  
  let result = code;
  
  // Transform JSX in render functions
  result = transformRenderFunctions(result);
  
  return result;
}

/**
 * Check if code contains JSX syntax
 * @param {string} code - JavaScript code
 * @returns {boolean} - True if JSX is detected
 */
function containsJSX(code) {
  return /<\w+[^>]*>/.test(code) || /<\/\w+>/.test(code) || /<\w+[^>]*\/>/.test(code);
}

/**
 * Transform render functions containing JSX
 * @param {string} code - JavaScript code
 * @returns {string} - Transformed code
 */
function transformRenderFunctions(code) {
  // Pattern to match render functions with JSX
  return code.replace(
    /render:\s*\([^)]*\)\s*=>\s*\(([\s\S]*?)\)(?=\s*[,}])/g,
    (match, jsxContent) => {
      const transpiled = transformJSXContent(jsxContent.trim());
      // Extract the parameter list from the original match
      const paramMatch = match.match(/render:\s*\(([^)]*)\)/);
      const params = paramMatch ? paramMatch[1] : '';
      return `render: (${params}) => ${transpiled}`;
    }
  );
}

/**
 * Transform JSX content to createElement calls
 * @param {string} jsx - JSX content
 * @returns {string} - createElement calls
 */
function transformJSXContent(jsx) {
  // Handle the root element and its children
  return transformJSXElement(jsx);
}

/**
 * Transform a single JSX element
 * @param {string} jsx - JSX element string
 * @returns {string} - createElement call
 */
function transformJSXElement(jsx) {
  jsx = jsx.trim();
  
  // Handle self-closing tags
  const selfClosingMatch = jsx.match(/^<(\w+)([^>]*?)\/>/);
  if (selfClosingMatch) {
    const [, tagName, attrs] = selfClosingMatch;
    const props = parseAttributes(attrs);
    return `FeexVeb.createElement('${tagName}'${props})`;
  }
  
  // Handle opening tag
  const openTagMatch = jsx.match(/^<(\w+)([^>]*?)>/);
  if (!openTagMatch) {
    // Not a JSX element, return as-is (might be text or expression)
    return jsx;
  }
  
  const [openTag, tagName, attrs] = openTagMatch;
  const props = parseAttributes(attrs);
  
  // Find the matching closing tag
  const closingTag = `</${tagName}>`;
  const closingIndex = findMatchingClosingTag(jsx, tagName, openTag.length);
  
  if (closingIndex === -1) {
    // No closing tag found, treat as self-closing
    return `FeexVeb.createElement('${tagName}'${props})`;
  }
  
  // Extract content between tags
  const content = jsx.substring(openTag.length, closingIndex);
  const children = parseChildren(content);
  
  if (children.length === 0) {
    return `FeexVeb.createElement('${tagName}'${props})`;
  } else if (children.length === 1) {
    return `FeexVeb.createElement('${tagName}'${props}, ${children[0]})`;
  } else {
    return `FeexVeb.createElement('${tagName}'${props}, ${children.join(', ')})`;
  }
}

/**
 * Find the matching closing tag for an element
 * @param {string} jsx - JSX string
 * @param {string} tagName - Tag name to match
 * @param {number} startIndex - Start searching from this index
 * @returns {number} - Index of closing tag or -1 if not found
 */
function findMatchingClosingTag(jsx, tagName, startIndex) {
  let depth = 1;
  let index = startIndex;
  
  while (index < jsx.length && depth > 0) {
    const openMatch = jsx.substring(index).match(new RegExp(`<${tagName}[^>]*>`));
    const closeMatch = jsx.substring(index).match(new RegExp(`</${tagName}>`));
    
    let nextOpen = openMatch ? index + jsx.substring(index).indexOf(openMatch[0]) : Infinity;
    let nextClose = closeMatch ? index + jsx.substring(index).indexOf(closeMatch[0]) : Infinity;
    
    if (nextClose < nextOpen) {
      depth--;
      if (depth === 0) {
        return nextClose;
      }
      index = nextClose + closeMatch[0].length;
    } else if (nextOpen < Infinity) {
      depth++;
      index = nextOpen + openMatch[0].length;
    } else {
      break;
    }
  }
  
  return -1;
}

/**
 * Parse children content (text, expressions, nested elements)
 * @param {string} content - Content between JSX tags
 * @returns {Array<string>} - Array of child expressions
 */
function parseChildren(content) {
  if (!content.trim()) {
    return [];
  }
  
  const children = [];
  let index = 0;
  
  while (index < content.length) {
    // Skip whitespace
    while (index < content.length && /\s/.test(content[index])) {
      index++;
    }
    
    if (index >= content.length) break;
    
    // Check for JSX element
    if (content[index] === '<') {
      const elementMatch = content.substring(index).match(/^<\w+[^>]*>/);
      if (elementMatch) {
        const tagName = elementMatch[0].match(/<(\w+)/)[1];
        const endIndex = findMatchingClosingTag(content, tagName, index + elementMatch[0].length);
        
        if (endIndex !== -1) {
          const element = content.substring(index, endIndex + `</${tagName}>`.length);
          children.push(transformJSXElement(element));
          index = endIndex + `</${tagName}>`.length;
          continue;
        }
      }
    }
    
    // Check for expression
    if (content[index] === '{') {
      const braceEnd = findMatchingBrace(content, index);
      if (braceEnd !== -1) {
        const expression = content.substring(index + 1, braceEnd);
        children.push(expression);
        index = braceEnd + 1;
        continue;
      }
    }
    
    // Handle text content
    let textEnd = index;
    while (textEnd < content.length && content[textEnd] !== '<' && content[textEnd] !== '{') {
      textEnd++;
    }
    
    if (textEnd > index) {
      const text = content.substring(index, textEnd).trim();
      if (text) {
        children.push(`'${text.replace(/'/g, "\\'")}'`);
      }
      index = textEnd;
    } else {
      index++;
    }
  }
  
  return children;
}

/**
 * Find matching closing brace
 * @param {string} content - Content string
 * @param {number} startIndex - Index of opening brace
 * @returns {number} - Index of closing brace or -1
 */
function findMatchingBrace(content, startIndex) {
  let depth = 1;
  let index = startIndex + 1;
  
  while (index < content.length && depth > 0) {
    if (content[index] === '{') {
      depth++;
    } else if (content[index] === '}') {
      depth--;
    }
    index++;
  }
  
  return depth === 0 ? index - 1 : -1;
}

/**
 * Parse JSX attributes into props object string
 * @param {string} attrString - Attribute string
 * @returns {string} - Props parameter for createElement
 */
function parseAttributes(attrString) {
  if (!attrString || !attrString.trim()) {
    return ', null';
  }
  
  const props = [];
  const trimmed = attrString.trim();
  
  // Parse string attributes: class="value"
  const stringAttrs = [...trimmed.matchAll(/(\w+)=["']([^"']+)["']/g)];
  for (const [, name, value] of stringAttrs) {
    props.push(`${name}: '${value}'`);
  }
  
  // Parse expression attributes: onclick={handler}
  const exprAttrs = [...trimmed.matchAll(/(\w+)=\{([^}]+)\}/g)];
  for (const [, name, value] of exprAttrs) {
    props.push(`${name}: ${value}`);
  }
  
  // Parse boolean attributes: disabled
  const boolAttrs = [...trimmed.matchAll(/\b(\w+)(?=\s|$)/g)];
  for (const [, name] of boolAttrs) {
    // Skip if it's part of a key=value pair
    if (!trimmed.includes(`${name}=`)) {
      props.push(`${name}: true`);
    }
  }
  
  return props.length > 0 ? `, { ${props.join(', ')} }` : ', null';
}
