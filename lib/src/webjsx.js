/**
 * @module webjsx
 * @description This module provides JSX functionality using FeexVeb's custom JSX runtime
 * for server-side rendering and client-side hydration.
 */

import { jsx, jsxs, Fragment as FeexVebFragment, createElement as createDomElement, renderToString as renderVNodeToString } from './jsx-runtime.js';

/**
 * @typedef {Object} VNode
 * @property {string|Function|Symbol} type - The type of the virtual node (e.g., 'div', a component function, or Fragment).
 * @property {Object} props - The properties (attributes and event listeners) of the node.
 * @property {Array<VNode|string>} children - The children of the node.
 */

/**
 * JSX factory function that creates virtual DOM nodes using mono-jsx.
 * This function is used by the JSX transpiler to create elements.
 *
 * @param {string|Function|Symbol} type - The type of the element (e.g., 'div', a component function, or `Fragment`).
 * @param {Object} [props={}] - The properties/attributes of the element.
 * @param {...(VNode|string)} children - The children of the element.
 * @returns {VNode} A virtual DOM node object.
 */
export const createElement = (type, props, ...children) => {
  // Flatten children and filter out null/undefined
  const flatChildren = children.flat().filter(child => child !== null && child !== undefined);

  // Use mono-jsx's jsx function for single child or jsxs for multiple children
  if (flatChildren.length === 0) {
    return jsx(type, props || {});
  } else if (flatChildren.length === 1) {
    return jsx(type, { ...(props || {}), children: flatChildren[0] });
  } else {
    return jsxs(type, { ...(props || {}), children: flatChildren });
  }
};

/**
 * Fragment component for grouping multiple elements without adding an extra DOM node.
 * Uses FeexVeb's Fragment implementation.
 *
 * @type {Symbol}
 */
export const Fragment = FeexVebFragment;

/**
 * Creates a real DOM node from a FeexVeb virtual DOM node.
 * This function handles the conversion from FeexVeb's VNode structure to actual DOM elements.
 *
 * @param {VNode|string|number} vnode - The virtual DOM node or a primitive value to convert to a DOM node.
 * @returns {HTMLElement|Text|DocumentFragment} The created DOM Node.
 */
export const createDomNode = createDomElement;

/**
 * Applies a virtual DOM to a container element using mono-jsx.
 * This function performs a simple replace operation for now, but could be enhanced
 * with more sophisticated diffing in the future.
 *
 * @param {HTMLElement} element - The container DOM element to update.
 * @param {VNode} vdom - The new virtual DOM structure to render.
 */
export const applyDiff = (element, vdom) => {
  if (!element) {
    console.error("applyDiff requires a valid DOM element.");
    return;
  }

  // Simple implementation: clear and re-render
  // In a production environment, this could be enhanced with proper diffing
  element.innerHTML = '';

  if (vdom) {
    const newNode = createDomNode(vdom);
    if (newNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {
      element.appendChild(newNode);
    } else {
      element.appendChild(newNode);
    }
  }
};

/**
 * Server-side rendering function that converts a VNode to HTML string.
 * This uses FeexVeb's custom JSX runtime for server-side rendering.
 *
 * @param {VNode} vnode - The virtual DOM node to render to HTML.
 * @returns {string} The HTML string representation.
 */
export const renderToString = renderVNodeToString;
