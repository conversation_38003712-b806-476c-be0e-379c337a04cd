/**
 * @module renderer
 * @description Unified rendering utilities for both client and server-side rendering
 */

import { applyDiff } from './webjsx.js';
import { processHtmx } from './htmx_integration.js';

/**
 * Client-side renderer utilities
 */
export const clientRenderer = {
  /**
   * Renders VDOM to a container and processes HTMX
   * @param {HTMLElement} container - Target container
   * @param {Object} vdom - Virtual DOM to render
   * @param {Object} options - Rendering options
   */
  render(container, vdom, options = {}) {
    const { processHtmxAfter = true } = options;
    
    applyDiff(container, vdom);
    
    if (processHtmxAfter && globalThis.htmx && globalThis.htmx.process) {
      processHtmx(container);
    }
  },

  /**
   * Renders a component instance
   * @param {Object} componentInstance - Component instance with render method
   * @param {HTMLElement} target - Target element (shadow root or light DOM)
   * @param {Object} options - Rendering options
   */
  renderComponent(componentInstance, target, options = {}) {
    const { processHtmxInShadow = true } = options;
    
    const vdom = componentInstance.render();
    applyDiff(target, vdom);

    if (globalThis.htmx && globalThis.htmx.process) {
      if (target.shadowRoot && processHtmxInShadow) {
        processHtmx(target.shadowRoot);
      } else if (!target.shadowRoot) {
        processHtmx(target);
      }
    }
  },

  /**
   * Creates a render function for a component
   * @param {Function} renderFn - Component render function
   * @param {Object} ctx - Component context
   * @param {Object} options - Rendering options
   * @returns {Function} Optimized render function
   */
  createComponentRenderer(renderFn, ctx, options = {}) {
    const { 
      processHtmxInShadow = true,
      debounceMs = 0 
    } = options;

    let timeoutId = null;
    
    const doRender = () => {
      const vdom = renderFn(ctx);
      const target = ctx.shadow || ctx.element;
      applyDiff(target, vdom);

      if (globalThis.htmx && globalThis.htmx.process) {
        if (ctx.shadow && processHtmxInShadow) {
          processHtmx(ctx.shadow);
        } else if (!ctx.shadow) {
          processHtmx(ctx.element);
        }
      }
    };

    if (debounceMs > 0) {
      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(doRender, debounceMs);
      };
    }

    return doRender;
  }
};

/**
 * Attribute serialization utilities (used by both client and server)
 */
export const attributeHelpers = {
  /**
   * Serializes props to HTML attributes string
   * @param {Object} props - Properties object
   * @param {Object} options - Serialization options
   * @returns {string} Attribute string
   */
  serialize(props, options = {}) {
    const { 
      excludeKeys = ['children'], 
      escapeHTML = true,
      booleanAsPresence = true 
    } = options;

    if (!props || typeof props !== 'object') {
      return '';
    }

    return Object.keys(props)
      .filter(key => props[key] != null && !excludeKeys.includes(key))
      .map(key => {
        const value = props[key];
        
        // Handle boolean attributes
        if (typeof value === 'boolean') {
          return booleanAsPresence && value ? ` ${key}` : '';
        }
        
        // Handle function attributes (skip)
        if (typeof value === 'function') {
          return '';
        }
        
        // Handle regular attributes
        let stringValue = String(value);
        if (escapeHTML) {
          stringValue = stringValue
            .replace(/&/g, '&amp;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
        }
          
        return ` ${key}="${stringValue}"`;
      })
      .join('');
  },

  /**
   * Copies data attributes from one element to another
   * @param {HTMLElement} source - Source element
   * @param {HTMLElement} target - Target element
   * @param {Object} options - Copy options
   */
  copyDataAttributes(source, target, options = {}) {
    const { exclude = ['component'], transform = null } = options;
    
    Object.keys(source.dataset).forEach(key => {
      if (!exclude.includes(key)) {
        const attrName = transform ? transform(key) : key.replace(/([A-Z])/g, '-$1').toLowerCase();
        target.setAttribute(attrName, source.dataset[key]);
      }
    });
  }
};

/**
 * Performance optimization utilities
 */
export const performanceHelpers = {
  /**
   * Creates a debounced function
   * @param {Function} fn - Function to debounce
   * @param {number} delay - Delay in milliseconds
   * @returns {Function} Debounced function
   */
  debounce(fn, delay) {
    let timeoutId = null;
    return (...args) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => fn(...args), delay);
    };
  },

  /**
   * Creates a throttled function
   * @param {Function} fn - Function to throttle
   * @param {number} delay - Delay in milliseconds
   * @returns {Function} Throttled function
   */
  throttle(fn, delay) {
    let lastExecution = 0;
    return (...args) => {
      const now = Date.now();
      if (now - lastExecution >= delay) {
        lastExecution = now;
        fn(...args);
      }
    };
  },

  /**
   * Creates a render scheduler for batching updates
   * @param {Function} renderFn - Render function
   * @returns {Object} Scheduler with schedule and flush methods
   */
  createRenderScheduler(renderFn) {
    let scheduled = false;
    let frameId = null;

    const flush = () => {
      scheduled = false;
      frameId = null;
      renderFn();
    };

    const schedule = () => {
      if (!scheduled) {
        scheduled = true;
        frameId = requestAnimationFrame(flush);
      }
    };

    const cancel = () => {
      if (frameId) {
        cancelAnimationFrame(frameId);
        scheduled = false;
        frameId = null;
      }
    };

    return { schedule, flush, cancel };
  }
};

/**
 * Error handling utilities for rendering
 */
export const errorHelpers = {
  /**
   * Creates an error boundary for component rendering
   * @param {Function} renderFn - Component render function
   * @param {Function} errorFallback - Error fallback render function
   * @returns {Function} Safe render function
   */
  createErrorBoundary(renderFn, errorFallback) {
    return (...args) => {
      try {
        return renderFn(...args);
      } catch (error) {
        console.error('Render error:', error);
        return errorFallback ? errorFallback(error, ...args) : null;
      }
    };
  },

  /**
   * Creates a safe renderer that handles errors gracefully
   * @param {Object} options - Error handling options
   * @returns {Function} Safe render function
   */
  createSafeRenderer(options = {}) {
    const { 
      onError = console.error,
      fallbackContent = '<!-- Render Error -->',
      rethrow = false 
    } = options;

    return (renderFn, ...args) => {
      try {
        return renderFn(...args);
      } catch (error) {
        onError('Safe renderer caught error:', error);
        if (rethrow) {
          throw error;
        }
        return fallbackContent;
      }
    };
  }
};

/**
 * Development helpers for debugging renders
 */
export const devHelpers = {
  /**
   * Logs render performance
   * @param {string} componentName - Name of the component
   * @param {Function} renderFn - Render function
   * @returns {Function} Instrumented render function
   */
  instrumentRender(componentName, renderFn) {
    return (...args) => {
      const startTime = performance.now();
      const result = renderFn(...args);
      const endTime = performance.now();
      
      if (endTime - startTime > 16) { // Log slow renders (>16ms)
        console.warn(`Slow render detected in ${componentName}: ${(endTime - startTime).toFixed(2)}ms`);
      }
      
      return result;
    };
  },

  /**
   * Creates a render tracer for debugging
   * @param {string} componentName - Name of the component
   * @returns {Object} Tracer with start and end methods
   */
  createRenderTracer(componentName) {
    let renderCount = 0;
    
    return {
      start() {
        renderCount++;
        console.group(`🎨 Render #${renderCount}: ${componentName}`);
        return performance.now();
      },
      
      end(startTime, vdom) {
        const duration = performance.now() - startTime;
        console.log(`Duration: ${duration.toFixed(2)}ms`);
        console.log('VDOM:', vdom);
        console.groupEnd();
      }
    };
  }
};