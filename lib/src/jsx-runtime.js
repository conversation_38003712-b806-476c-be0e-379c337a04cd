/**
 * @file FeexVeb JSX Runtime
 * @description Custom JSX runtime for FeexVeb that provides server-side rendering
 * and client-side hydration without complex transpilation.
 */

/**
 * JSX factory function for creating virtual DOM elements
 * @param {string|Function} type - Element type or component function
 * @param {Object} props - Element properties/attributes
 * @param {...any} children - Child elements
 * @returns {Object} Virtual DOM element
 */
export function jsx(type, props) {
  const { children, ...otherProps } = props || {};
  
  return {
    type,
    props: otherProps,
    children: Array.isArray(children) ? children : (children !== undefined ? [children] : [])
  };
}

/**
 * JSX factory function for elements with multiple children
 * @param {string|Function} type - Element type or component function
 * @param {Object} props - Element properties/attributes
 * @returns {Object} Virtual DOM element
 */
export function jsxs(type, props) {
  return jsx(type, props);
}

/**
 * Fragment component for grouping elements without wrapper
 */
export const Fragment = Symbol('Fragment');

/**
 * Renders a virtual DOM element to HTML string
 * @param {Object} vnode - Virtual DOM element
 * @returns {string} HTML string
 */
export function renderToString(vnode) {
  if (typeof vnode === 'string' || typeof vnode === 'number') {
    return String(vnode);
  }
  
  if (!vnode || typeof vnode !== 'object') {
    return '';
  }
  
  if (vnode.type === Fragment) {
    return vnode.children.map(child => renderToString(child)).join('');
  }
  
  if (typeof vnode.type === 'function') {
    // Handle component functions
    const result = vnode.type(vnode.props);
    return renderToString(result);
  }
  
  if (typeof vnode.type === 'string') {
    const { type, props, children } = vnode;
    let html = `<${type}`;
    
    // Add attributes
    if (props) {
      for (const [key, value] of Object.entries(props)) {
        if (key === 'children') continue;
        
        if (key === 'className') {
          html += ` class="${escapeHtml(value)}"`;
        } else if (key === 'htmlFor') {
          html += ` for="${escapeHtml(value)}"`;
        } else if (typeof value === 'boolean') {
          if (value) {
            html += ` ${key}`;
          }
        } else if (value !== null && value !== undefined && !key.startsWith('on')) {
          html += ` ${key}="${escapeHtml(String(value))}"`;
        }
      }
    }
    
    // Self-closing tags
    const selfClosingTags = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];
    if (selfClosingTags.includes(type)) {
      html += ' />';
      return html;
    }
    
    html += '>';
    
    // Add children
    if (children && children.length > 0) {
      html += children.map(child => renderToString(child)).join('');
    }
    
    html += `</${type}>`;
    return html;
  }
  
  return '';
}

/**
 * Escapes HTML special characters
 * @param {string} str - String to escape
 * @returns {string} Escaped string
 */
function escapeHtml(str) {
  const htmlEscapes = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  };
  
  return String(str).replace(/[&<>"']/g, (match) => htmlEscapes[match]);
}

/**
 * Creates a DOM element from a virtual DOM element (client-side)
 * @param {Object} vnode - Virtual DOM element
 * @returns {HTMLElement|Text|DocumentFragment} DOM element
 */
export function createElement(vnode) {
  if (typeof vnode === 'string' || typeof vnode === 'number') {
    return document.createTextNode(String(vnode));
  }
  
  if (!vnode || typeof vnode !== 'object') {
    return document.createComment('Empty VNode');
  }
  
  if (vnode.type === Fragment) {
    const fragment = document.createDocumentFragment();
    vnode.children.forEach(child => {
      fragment.appendChild(createElement(child));
    });
    return fragment;
  }
  
  if (typeof vnode.type === 'function') {
    const result = vnode.type(vnode.props);
    return createElement(result);
  }
  
  if (typeof vnode.type === 'string') {
    const element = document.createElement(vnode.type);
    
    // Set attributes and event listeners
    if (vnode.props) {
      for (const [key, value] of Object.entries(vnode.props)) {
        if (key === 'children') continue;
        
        if (key.startsWith('on') && typeof value === 'function') {
          const eventName = key.substring(2).toLowerCase();
          element.addEventListener(eventName, value);
        } else if (key === 'className') {
          element.className = value;
        } else if (key === 'htmlFor') {
          element.setAttribute('for', value);
        } else if (typeof value === 'boolean') {
          if (value) {
            element.setAttribute(key, '');
          }
        } else if (value !== null && value !== undefined) {
          element.setAttribute(key, String(value));
        }
      }
    }
    
    // Add children
    if (vnode.children && vnode.children.length > 0) {
      vnode.children.forEach(child => {
        element.appendChild(createElement(child));
      });
    }
    
    return element;
  }
  
  return document.createComment('Unknown VNode');
}
