<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <script type="importmap">
    {
      "imports": {
        "@maverick-js/signals": "https://esm.sh/@maverick-js/signals@5.11.5"
      }
    }
    </script>
</head>
<body>
    <h1>Simple Test</h1>
    <div id="output"></div>
    
    <script type="module">
        console.log('Module script starting');
        
        try {
            // Test if we can import the signals library
            const { signal } = await import('@maverick-js/signals');
            console.log('Signals imported successfully:', signal);
            
            // Test basic signal functionality
            const count = signal(0);
            console.log('Signal created, initial value:', count());
            
            count.set(42);
            console.log('Signal updated, new value:', count());
            
            document.getElementById('output').innerHTML = `
                <p style="color: green;">✅ Signals library loaded successfully!</p>
                <p>Signal value: ${count()}</p>
            `;
            
        } catch (error) {
            console.error('Error loading signals:', error);
            document.getElementById('output').innerHTML = `
                <p style="color: red;">❌ Failed to load signals library</p>
                <p>Error: ${error.message}</p>
            `;
        }
    </script>
</body>
</html>
