{"tasks": {"dev": "deno run --watch --allow-net --allow-read --allow-env server/server.ts"}, "imports": {"@std/assert": "jsr:@std/assert@1", "@maverick-js/signals": "npm:@maverick-js/signals@^5.11.5", "preact": "https://esm.sh/preact@10.19.3", "preact/jsx-runtime": "https://esm.sh/preact@10.19.3/jsx-runtime", "preact-render-to-string": "https://esm.sh/preact-render-to-string@6.3.1"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact"}}